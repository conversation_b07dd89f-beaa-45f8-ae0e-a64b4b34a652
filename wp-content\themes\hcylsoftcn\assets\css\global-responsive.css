/* 全局响应式样式 - 合并版本 */

/* 平板端样式 (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
  .wrapper {
    overflow: hidden;
  }

  .company-profile-box {
    max-width: 1210px;
    margin: 0 auto;
    width: 94%;
  }

  /* 解决方案页面平板样式 */
  .pad-solution-title {
    font-size: 20px;
    padding-top: 40px;
    padding-bottom: 20px;
    text-align: center;
  }

  .padswiper-container-solution {
    position: relative;
    margin-top: 20px;
  }

  .padswiper-container-solution .swiper-slide {
    margin-right: 0 !important;
  }

  .padswiper-container-solution .swiper-button-prev:after,
  .swiper-container-rtl .swiper-button-next:after {
    content: "" !important;
  }

  .padswiper-container-solution .swiper-button-next:after,
  .swiper-container-rtl .swiper-button-prev:after {
    content: "" !important;
  }

  .padswiper-container-solution .swiper-button-prev {
    left: 45px !important;
  }

  .padswiper-container-solution .swiper-button-next {
    right: 45px !important;
  }

  .padswiper-container-solution .swiper-wrapper {
    width: calc(100% - 207px) !important;
    margin: 0 auto;
  }

  .padswiper-box {
    padding-left: 0.15rem;
    padding-right: 0.15rem;
  }

  .padswiper-box img {
    width: 100%;
    height: 201px;
  }

  /* 合作伙伴平板样式 */
  .padswiper-partner-box {
    height: 90px;
    border: 1px solid #eeeeee;
    border-radius: 10px;
    position: relative;
    margin-left: 8px;
    margin-right: 8px;
  }

  .padswiper-partner-box img {
    height: 47px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .padswiper-container-partner {
    position: relative;
    width: calc(100% - 150px) !important;
    margin: 0 auto;
    margin-top: 20px;
  }

  .padswiper-container-partner .swiper-button-prev:after,
  .swiper-container-rtl .swiper-button-next:after {
    content: "" !important;
  }

  .padswiper-container-partner .swiper-button-next:after,
  .swiper-container-rtl .swiper-button-prev:after {
    content: "" !important;
  }

  .padswiper-container-partner .swiper-button-prev {
    left: -55px !important;
  }

  .padswiper-container-partner .swiper-button-next {
    right: -55px !important;
  }

  .padswiper-container-partner .swiper-slide {
    margin-right: 0 !important;
  }
}

@media (max-width: 1024px) {
  .wrapper {
    overflow: hidden;
  }

  .company-profile-box {
    max-width: 1210px;
    margin: 0 auto;
    width: 94%;
  }
}

@media (max-width: 768px) {
  .wrapper {
    width: 768px;
    overflow: hidden;
  }

  .company-profile-box {
    max-width: 590px;
    margin: 0 auto;
    width: 94%;
    height: auto;
  }
}

/* 移动端样式 (最大767px) */
@media (max-width: 767px) {
  * {
    box-sizing: border-box;
  }

  .wrapper {
    width: 100%;
    overflow: hidden;
  }

  .company-profile-box {
    width: 100%;
  }

  /* 解决方案页面移动端样式 */
  .swiper-container-menhu {
    position: relative;
    margin-top: 0.15rem;
  }

  .swiper-container-jigou,
  .swiper-container-guizhi,
  .swiper-container-tian,
  .swiper-container-huiyi,
  .swiper-container-huiyishi,
  .swiper-container-hetong,
  .swiper-container-jindu,
  .swiper-container-touzi {
    position: relative;
  }

  .swiper-container-menhu .swiper-slide p {
    font-size: 0.2rem;
    color: #4f7fe8;
    text-align: center;
  }

  .swiper-container-menhu .swiper-slide img {
    width: 100%;
  }

  .swiper-pagination {
    bottom: -10px !important;
  }

  .wrapper-pad {
    padding-top: 0;
  }

  .mb-tabs {
    background: #fff;
    overflow: hidden;
    border-bottom: 0.1rem solid #f5f5f5;
  }

  .mb-tabs li {
    text-align: center;
  }

  .mb-tabs li a {
    color: #333;
    font-size: 0.16rem;
    line-height: 0.56rem;
  }

  .mb-tabs li .active {
    color: #4f7fe8;
    position: relative;
  }

  .mb-tabs li .active::after {
    position: absolute;
    display: block;
    content: "";
    width: 0.22rem;
    height: 0.03rem;
    background: linear-gradient(90deg, #97b5f7, #4f7fe8);
    border-radius: 0.01rem;
    left: 50%;
    transform: translateX(-50%);
    bottom: -10px;
  }

  .pro-box-1 {
    background: #fff;
  }

  .product-box {
    padding-top: 0.1rem;
    padding-bottom: 0.1rem;
    padding-left: 0;
    padding-right: 0;
    box-sizing: border-box;
  }

  .product-title {
    font-size: 0.17rem;
  }

  .product-intro {
    font-size: 0.12rem;
    padding-top: 0.05rem;
    padding-left: 0.1rem;
    padding-right: 0.1rem;
    padding-bottom: 0.15rem;
  }

  .product-line {
    width: 19px;
    height: 1px;
  }

  .pro-content {
    padding-top: 0rem !important;
    padding-left: 0 !important;
  }
  .value-target-item2 {
    width: auto !important;
  }

  .pro-num {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .pro-name {
    margin-top: 0.15rem !important;
    width: calc(100% - 20px) !important;
  }

  .pro-content p {
    color: #666;
    line-height: 0.21rem;
    font-size: 0.1rem;
  }

  .application-scenarios-discribe p {
    line-height: 0.21rem;
    color: #666;
    font-size: 0.1rem;
  }

  .computer-img-container-mb {
    text-align: center;
  }

  .pro-img-1 {
    padding-right: 0.35rem;
    padding-left: 0.35rem;
  }

  .pro-img-1 img {
    margin: 0 auto;
  }

  .pro-intro {
    font-size: 0.13rem;
    padding-top: 0.05rem;
  }

  /* 关于我们页面移动端样式 */
  .mb-solution-title {
    color: #333;
    font-size: 20px;
    padding-top: 40px;
    padding-bottom: 20px;
    text-align: center;
    background: #fff;
  }

  .mb-solution-content {
    padding-left: 0.43rem;
    padding-right: 0.43rem;
    padding-bottom: 0.66rem;
    background: #fff;
    line-height: 24px;
  }

  /* 产品页面移动端样式 */
  .btn-container .btn {
    width: 1rem;
    margin-right: 0;
    margin-top: 0.2rem;
    font-size: 0.1rem;
  }

  .duban-xiangqing .title {
    font-size: 0.12rem;
    color: #333333;
  }

  .huiyiduban-box {
    margin-bottom: 0.1rem;
  }

  .duban-xiangqing {
    margin-top: 0.3rem;
  }

  .duban-xiangqing .text {
    font-size: 0.1rem;
    color: #666;
    line-height: 0.21rem;
  }
}
