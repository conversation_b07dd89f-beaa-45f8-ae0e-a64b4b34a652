/* 证照打印一体机页面专用样式 */

/* CSS变量定义 */
:root {
  --seal-print-primary: #1e40af;
  --seal-print-hover: #1d4ed8;
  --seal-print-text: #333;
  --seal-print-secondary: #666;
  --seal-print-bg: #f8f9fa;
  --seal-print-light-bg: #ffffff;
  --seal-print-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  --seal-print-hover-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --seal-print-border-radius: 12px;
  --seal-print-transition: all 0.3s ease;
}

/* 基础样式 */
.seal-print {
  font-family: "Noto Sans SC", sans-serif;
  line-height: 1.6;
  color: var(--seal-print-text);
}

.seal-print .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
#hero {
  padding: 0px;
}
/* Banner样式 */
.seal-print .banner-container {
  position: relative;
  width: 100%;
  height: 400px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.seal-print .banner-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 通用section样式 */
.seal-print section {
  padding: 80px 0;
}

.seal-print .section-header {
  text-align: center;
  margin-bottom: 50px;
}

.seal-print .section-header h2 {
  font-size: 36px;
  color: var(--seal-print-text);
  margin-bottom: 20px;
  font-weight: 600;
  position: relative;
  padding-bottom: 15px;
}

.seal-print .section-header h2::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(to right, var(--seal-print-primary), var(--seal-print-hover));
  border-radius: 2px;
}

.seal-print .section-desc {
  font-size: 18px;
  color: var(--seal-print-secondary);
  max-width: 800px;
  margin: 0 auto;
}

/* 产品介绍样式 */
.seal-print #market-background {
  background: var(--seal-print-light-bg);
}

.seal-print .market-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  margin-top: 40px;
}

.seal-print .intro-text p {
  font-size: 16px;
  line-height: 1.8;
  color: var(--seal-print-secondary);
  margin: 0;
}

.seal-print .market-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.seal-print .market-image video {
  border-radius: var(--seal-print-border-radius);
  box-shadow: var(--seal-print-shadow);
}

/* 主要亮点样式 */
.seal-print .print-section {
  background: var(--seal-print-bg);
  padding: 80px 0;
}

.seal-print .print-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.seal-print .print-header {
  text-align: center;
  margin-bottom: 60px;
}

.seal-print .print-header h2 {
  font-size: 36px;
  color: var(--seal-print-text);
  margin-bottom: 20px;
  font-weight: 600;
}

.seal-print .print-header .description {
  font-size: 18px;
  color: var(--seal-print-secondary);
  max-width: 800px;
  margin: 0 auto;
}

.seal-print .print-features {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin-bottom: 60px;
}

.seal-print .print-feature-item {
  text-align: center;
  padding: 0;
  background: var(--seal-print-light-bg);
  border-radius: var(--seal-print-border-radius);
  box-shadow: var(--seal-print-shadow);
  transition: var(--seal-print-transition);
  display: flex;
  flex-direction: column;
  align-items: stretch;
  overflow: hidden;
}

.seal-print .print-feature-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--seal-print-hover-shadow);
}

.seal-print .print-feature-image {
  width: 100%;
  height: 200px;
  margin-bottom: 0;
  display: block;
}

.seal-print .print-feature-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.seal-print .print-feature-item h3 {
  font-size: 20px;
  color: var(--seal-print-text);
  margin: 20px 20px 15px;
  font-weight: 600;
}

.seal-print .print-feature-item p {
  color: var(--seal-print-secondary);
  line-height: 1.6;
  margin: 0 20px 20px;
}

/* 市场需求样式 */
.seal-print .market-needs-section {
  background: var(--seal-print-light-bg);
  padding: 80px 0;
}

.seal-print .market-needs-content {
  max-width: 1200px;
  margin: 40px auto 0;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  padding: 0 20px;
}

.seal-print .needs-item {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  background: var(--seal-print-bg);
  padding: 50px 30px;
  border-radius: var(--seal-print-border-radius);
  transition: var(--seal-print-transition);
  box-shadow: var(--seal-print-shadow);
}

.seal-print .needs-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--seal-print-hover-shadow);
}

.seal-print .needs-icon {
  flex-shrink: 0;
  font-size: 24px;
  color: var(--seal-print-primary);
  padding: 15px;
  background: rgba(30, 64, 175, 0.1);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.seal-print .needs-text {
  flex: 1;
}

.seal-print .needs-text h3 {
  font-size: 20px;
  color: var(--seal-print-text);
  margin-bottom: 10px;
  font-weight: 600;
}

.seal-print .needs-text p {
  color: var(--seal-print-secondary);
  line-height: 1.6;
  margin: 0;
}

/* 解决方案样式 */
.seal-print .solution-section {
  background: #1f2937;
  padding: 80px 0;
}

.seal-print .solution-section .section-header h2 {
  color: #fff;
}

.seal-print .solution-content {
  max-width: 1200px;
  margin: 40px auto 0;
  padding: 0 20px;
}

.seal-print .solution-text {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 40px;
}

.seal-print .solution-text p {
  color: #e5e7eb;
  line-height: 1.8;
  font-size: 16px;
}

.seal-print .solution-image {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.seal-print .solution-image img {
  max-width: 100%;
  height: auto;
  border-radius: var(--seal-print-border-radius);
}

/* 核心功能模块样式 */
.seal-print #core-features {
  background: var(--seal-print-light-bg);
  padding: 80px 0;
}

.seal-print #core-features .section-header h2 {
  color: var(--seal-print-text);
}

.seal-print #core-features .vm-features-grid {
  display: flex;
  flex-direction: column;
  gap: 40px;
  margin-top: 40px;
}

.seal-print #core-features .vm-feature-card {
  width: 100%;
  background: var(--seal-print-light-bg);
  border: none;
  padding: 40px 0;
}

.seal-print #core-features .vm-feature-card .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  gap: 60px;
  flex-direction: row;
}

/* 偶数卡片反转布局 */
.seal-print #core-features .vm-feature-card:nth-child(even) .container {
  flex-direction: row-reverse;
}

.seal-print #core-features .vm-feature-image {
  flex: 1;
  height: auto;
  max-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.seal-print #core-features .vm-feature-image img {
  width: 100%;
  height: auto;
  object-fit: contain;
  border-radius: var(--seal-print-border-radius);
}

.seal-print #core-features .vm-feature-content {
  flex: 1;
  padding: 0;
}

.seal-print #core-features .vm-feature-content h3 {
  color: var(--seal-print-text);
  font-size: 24px;
  margin-bottom: 15px;
  font-weight: 600;
}

.seal-print #core-features .vm-feature-content p {
  color: var(--seal-print-secondary);
  font-size: 16px;
  line-height: 1.6;
}

/* 业务场景样式 */
.seal-print .business-scene-section {
  background: var(--seal-print-bg);
  padding: 80px 0;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e5e7eb' fill-opacity='0.3'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.seal-print .scene-content {
  max-width: 1200px;
  margin: 40px auto 0;
  padding: 0 20px;
  display: flex;
  flex-direction: column;
  gap: 60px;
}

.seal-print .scene-item {
  display: flex;
  gap: 40px;
  align-items: flex-start;
  background: var(--seal-print-light-bg);
  padding: 40px;
  border-radius: var(--seal-print-border-radius);
  box-shadow: var(--seal-print-shadow);
  transition: var(--seal-print-transition);
}

.seal-print .scene-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--seal-print-hover-shadow);
}

.seal-print .scene-image {
  flex: 0 0 300px;
  height: 200px;
  overflow: hidden;
  border-radius: var(--seal-print-border-radius);
}

.seal-print .scene-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.seal-print .scene-text {
  flex: 1;
}

.seal-print .scene-text h3 {
  font-size: 24px;
  color: var(--seal-print-text);
  margin-bottom: 20px;
  font-weight: 600;
}

.seal-print .scene-background {
  color: var(--seal-print-secondary);
  line-height: 1.6;
  margin-bottom: 15px;
  font-size: 16px;
}

.seal-print .scene-solution {
  color: var(--seal-print-secondary);
  line-height: 1.6;
  margin-bottom: 20px;
  font-size: 16px;
}

.seal-print .scene-list {
  margin-top: 20px;
}

.seal-print .scene-list h4 {
  font-size: 18px;
  color: var(--seal-print-text);
  margin-bottom: 15px;
  font-weight: 600;
}

.seal-print .scene-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.seal-print .scene-list li {
  color: var(--seal-print-secondary);
  line-height: 1.6;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
}

.seal-print .scene-list li::before {
  content: "•";
  color: var(--seal-print-primary);
  position: absolute;
  left: 0;
  font-weight: bold;
}

/* 数据安全与合规样式 */
.seal-print .security-section {
  background: #1f2937;
  padding: 80px 0;
}

.seal-print .security-section .section-header h2 {
  color: #fff;
}

.seal-print .security-section .section-desc {
  color: #e5e7eb;
}

.seal-print .security-grid {
  max-width: 1200px;
  margin: 40px auto 0;
  padding: 0 20px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
}

.seal-print .security-item {
  background: rgba(255, 255, 255, 0.05);
  padding: 30px;
  border-radius: var(--seal-print-border-radius);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: var(--seal-print-transition);
}

.seal-print .security-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-5px);
}

.seal-print .security-content h3 {
  color: #fff;
  font-size: 20px;
  margin-bottom: 15px;
  font-weight: 600;
}

.seal-print .security-content p {
  color: #e5e7eb;
  line-height: 1.6;
  font-size: 16px;
}

/* 设备参数样式 */
.seal-print .specs-section {
  background: var(--seal-print-light-bg);
  padding: 80px 0;
}

.seal-print .specs-grid {
  max-width: 1200px;
  margin: 40px auto 0;
  padding: 0 20px;
}

.seal-print .specs-wrapper {
  display: flex;
  gap: 60px;
  align-items: center;
}

.seal-print .specs-item {
  flex: 1;
}

.seal-print .specs-item h3 {
  font-size: 24px;
  color: var(--seal-print-text);
  margin-bottom: 30px;
  font-weight: 600;
}

.seal-print .specs-item ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.seal-print .specs-item li {
  color: var(--seal-print-secondary);
  line-height: 1.8;
  margin-bottom: 15px;
  font-size: 16px;
  display: flex;
  align-items: flex-start;
}

.seal-print .specs-item li span {
  color: var(--seal-print-text);
  font-weight: 600;
  min-width: 100px;
  flex-shrink: 0;
}

.seal-print .specs-image {
  flex: 0 0 400px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.seal-print .specs-image img {
  width: 100%;
  height: auto;
  border-radius: var(--seal-print-border-radius);
  box-shadow: var(--seal-print-shadow);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .seal-print .container {
    padding: 0 15px;
  }

  .seal-print .market-content {
    gap: 40px;
  }

  .seal-print #core-features .vm-feature-card .container {
    gap: 40px;
  }

  .seal-print .specs-wrapper {
    gap: 40px;
  }

  .seal-print .specs-image {
    flex: 0 0 350px;
  }
}

@media (max-width: 992px) {
  .seal-print section {
    padding: 60px 0;
  }

  .seal-print .section-header h2 {
    font-size: 32px;
  }

  .seal-print .print-features {
    grid-template-columns: repeat(2, 1fr);
  }

  .seal-print .market-needs-content {
    grid-template-columns: repeat(2, 1fr);
  }

  .seal-print .security-grid {
    grid-template-columns: 1fr;
  }

  .seal-print .market-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .seal-print #core-features .vm-feature-card .container {
    flex-direction: column !important;
    gap: 30px;
  }

  .seal-print #core-features .vm-feature-card:nth-child(even) .container {
    flex-direction: column !important;
  }

  .seal-print .specs-wrapper {
    flex-direction: column;
    gap: 30px;
  }

  .seal-print .specs-image {
    flex: none;
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
  }

  .seal-print .scene-item {
    flex-direction: column;
    gap: 20px;
  }

  .seal-print .scene-image {
    flex: none;
    width: 100%;
    height: 250px;
  }
}

@media (max-width: 768px) {
  .seal-print section {
    padding: 40px 0;
  }

  .seal-print .section-header h2 {
    font-size: 28px;
  }

  .seal-print .section-header {
    margin-bottom: 30px;
  }

  .seal-print .print-features {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .seal-print .market-needs-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .seal-print .needs-item {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .seal-print .needs-icon {
    align-self: center;
  }

  .seal-print .scene-content {
    gap: 40px;
  }

  .seal-print .scene-item {
    padding: 30px 20px;
  }

  .seal-print .scene-image {
    height: 200px;
  }

  .seal-print .security-item {
    padding: 20px;
  }

  .seal-print .banner-container {
    height: 300px;
  }
}

@media (max-width: 576px) {
  .seal-print .container {
    padding: 0 10px;
  }

  .seal-print section {
    padding: 30px 0;
  }

  .seal-print .section-header h2 {
    font-size: 24px;
  }

  .seal-print .print-header h2 {
    font-size: 24px;
  }

  .seal-print .print-feature-item h3 {
    font-size: 18px;
  }

  .seal-print .needs-text h3 {
    font-size: 18px;
  }

  .seal-print .scene-text h3 {
    font-size: 20px;
  }

  .seal-print .security-content h3 {
    font-size: 18px;
  }

  .seal-print .specs-item h3 {
    font-size: 20px;
  }

  .seal-print .banner-container {
    height: 250px;
  }

  .seal-print .scene-item {
    padding: 20px 15px;
  }

  .seal-print .security-item {
    padding: 15px;
  }

  .seal-print .specs-item li span {
    min-width: 80px;
  }
}
