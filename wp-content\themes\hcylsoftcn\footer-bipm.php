<?php

/**
 * The template for displaying the footer
 *
 * Contains the closing of the #content div and all content after.
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package hcylsoftcn
 */


get_template_part('template-parts/footer', 'common');
wp_footer();
?>


<script type="text/javascript">
    var tabsShow = document.getElementsByClassName('business-center-tab-center2');
    var tabs22 = document.getElementsByClassName('color12');
    var tabs32 = document.getElementsByClassName('color22');
    $('.show-target12').hide()
    $('.tab12').show()
    $('.tab22').hide()
    $('.tab32').hide()
    $('.tab42').hide()
    $('.triangle12').show()
    $('.triangle22').hide()
    $('.triangle32').hide()
    $('.triangle42').hide()
    $('.show-target1').hide()
    $('.tab1').show()
    $('.tab2').hide()
    $('.tab3').hide()
    $('.tab4').hide()
    $('.triangle1').show()
    $('.triangle2').hide()
    $('.triangle3').hide()
    $('.triangle4').hide()
    $('.code2').hide()
    $('.top2').hide()
    $(".right-a").each(function(i) {
        $(".right-a").eq(i).hover(function() {
            $('.code2').show()
            $('.code1').hide()
        }, function() {
            $('.code1').show()
            $('.code2').hide()
        });
    });
    $(".right-gotop").each(function(i) {
        $(".right-gotop").eq(i).hover(function() {
            $('.top2').show()
            $('.top1').hide()
        }, function() {
            $('.top1').show()
            $('.top2').hide()
        });
    });
    var swiper1 = new Swiper('.swiper-container-banner', {
        loop: true,
        pagination: {
            el: '.swiper-pagination',
        },
        autoplay: {
            delay: 4000,
        },
    })

    var swiper2 = new Swiper('.swiper-container-banner2', {
        loop: true,
        pagination: {
            el: '.swiper-pagination',
        },
        autoplay: {
            delay: 4000,
        },

    })
    var swiper3 = new Swiper('.swiper-container-banner3', {
        loop: true,
        pagination: {
            el: '.swiper-pagination',
        },
        autoplay: {
            delay: 4000,
        },
    })
    swiper3.on('slideChangeTransitionEnd', () => {
        let i = swiper3.activeIndex - 1
        if (i == -1) {
            i = 3
        }
        if (i == 4) {
            i = 0
        }

        for (let x1 = 0; x1 < tabs22.length; x1++) {
            if (i == x1) {
                tabsShow[x1].className = 'business-center-tab-center2 business-center-tab-center-active2';
                tabs22[x1].className = 'color12 color32';
                tabs32[x1].className = 'color22 color32';
                tabs32[x1].style.display = 'block';
            } else {
                tabs22[x1].className = 'color12';
                tabs32[x1].className = 'color22';
                tabsShow[x1].className = 'business-center-tab-center2';
                tabs32[x1].style.display = 'none';
                tabs22[x1].style.marginTop = '0';
            }

        }

        if (i == 0) {
            $('.show-target12').hide()
            $('.hide-target12').show()
            $('.tab12').show()
            $('.tab22').hide()
            $('.tab32').hide()
            $('.tab42').hide()
            $('.show-target22').show()
            $('.hide-target22').hide()
            $('.show-target32').show()
            $('.hide-target32').hide()
            $('.show-target42').show()
            $('.hide-target42').hide()
            $('.triangle12').show()
            $('.triangle22').hide()
            $('.triangle32').hide()
            $('.triangle42').hide()
        } else if (i == 1) {
            $('.tab22').show()
            $('.tab12').hide()
            $('.tab32').hide()
            $('.tab42').hide()
            $('.show-target22').hide()
            $('.hide-target22').show()
            $('.show-target12').show()
            $('.hide-target12').hide()
            $('.show-target32').show()
            $('.hide-target32').hide()
            $('.show-target42').show()
            $('.hide-target42').hide()
            $('.triangle22').show()
            $('.triangle12').hide()
            $('.triangle32').hide()
            $('.triangle42').hide()
        } else if (i == 2) {
            $('.tab32').show()
            $('.tab22').hide()
            $('.tab12').hide()
            $('.tab42').hide()
            $('.show-target32').hide()
            $('.hide-target32').show()
            $('.show-target22').show()
            $('.hide-target22').hide()
            $('.show-target12').show()
            $('.hide-target12').hide()
            $('.show-target42').show()
            $('.hide-target42').hide()
            $('.triangle32').show()
            $('.triangle12').hide()
            $('.triangle22').hide()
            $('.triangle42').hide()
        } else if (i == 3) {
            $('.tab42').show()
            $('.tab22').hide()
            $('.tab32').hide()
            $('.tab12').hide()
            $('.show-target42').hide()
            $('.hide-target42').show()
            $('.show-target22').show()
            $('.hide-target22').hide()
            $('.show-target32').show()
            $('.hide-target32').hide()
            $('.show-target12').show()
            $('.hide-target12').hide()
            $('.triangle42').show()
            $('.triangle12').hide()
            $('.triangle22').hide()
            $('.triangle32').hide()
        }


    });

    //视频播放
    const video = document.getElementsByTagName('video')[0];
    const playVideoC = document.getElementsByClassName('playVideoC')[0];
    if (video != undefined) {
        video.addEventListener('play', function() {
            playVideoC.style.display = 'none'
        });
        video.addEventListener('pause', function() {
            playVideoC.style.display = 'block'
        });
        video.addEventListener('ended', function() {
            playVideoC.style.display = 'block'
        });
    }


    function playVideo() {
        if (video.paused) {
            video.play()

        } else {
            video.pause()
        }
    }
    //移动端视频播放
    const video2 = document.getElementsByTagName('video')[1];
    const playVideoC2 = document.getElementsByClassName('playVideoC')[1];
    if (video2 != undefined) {
        video2.addEventListener('play', function() {
            playVideoC2.style.display = 'none'
        });
        video2.addEventListener('pause', function() {
            playVideoC2.style.display = 'block'
        });
        video2.addEventListener('ended', function() {
            playVideoC2.style.display = 'block'
        });
    }


    function playVideo2() {
        // video.requestPictureInPicture()
        if (video2.paused) {
            video2.play()

        } else {
            video2.pause()
            // document.exitPictureInPicture()
        }
    }
    const videoShow = document.getElementsByClassName('title-center-swiper-right')[0];
    const videoShow2 = document.getElementsByClassName('title-center-swiper-right')[1];
    // 监听页面滚动事件
    window.addEventListener('scroll', function() {
        var scrollRatio = (document.body.clientHeight + window.pageYOffset) / document.body.offsetHeight;
        if (!video.paused && scrollRatio > 1.1) {
            videoShow.className = 'title-center-swiper-right fixed'
        } else {
            videoShow.className = 'title-center-swiper-right'
        }
        if (!video2.paused && scrollRatio > 1.1) {
            videoShow2.className = 'title-center-swiper-right fixed'
        } else {
            videoShow2.className = 'title-center-swiper-right'
        }
    });


    //pc端
    //公司名称校验
    const cpname = document.getElementById("cpname");
    const roleCpname = document.getElementsByClassName("roleCpname")[0];
    roleCpname.style.display = 'none'
    cpname.addEventListener("change", function() {
        if (this.value) {
            roleCpname.style.display = 'none'
        } else {
            roleCpname.style.display = 'block'
        }
    });
    //联系人校验
    const name = document.getElementById("name");
    const roleName = document.getElementsByClassName("roleName")[0];
    roleName.style.display = 'none'
    name.addEventListener("change", function() {
        if (this.value) {
            roleName.style.display = 'none'
        } else {
            roleName.style.display = 'block'
        }
    });
    //联系电话校验
    const phone = document.getElementById("phone");
    const rolePhone = document.getElementsByClassName("rolePhone")[0];
    rolePhone.style.display = 'none'
    phone.addEventListener("change", function() {
        if (this.value) {
            rolePhone.style.display = 'none'
        } else {
            rolePhone.style.display = 'block'
        }
    });


    //移动端
    //公司名称校验
    const cpname2 = document.getElementById("cpname2");
    const roleCpname2 = document.getElementsByClassName("roleCpname2")[0];
    roleCpname2.style.display = 'none'
    cpname2.addEventListener("change", function() {
        if (this.value) {
            roleCpname2.style.display = 'none'
        } else {
            roleCpname2.style.display = 'block'
        }
    });
    //联系人校验
    const name2 = document.getElementById("name2");
    const roleName2 = document.getElementsByClassName("roleName2")[0];
    roleName2.style.display = 'none'
    name2.addEventListener("change", function() {
        if (this.value) {
            roleName2.style.display = 'none'
        } else {
            roleName2.style.display = 'block'
        }
    });
    //联系电话校验
    const phone2 = document.getElementById("phone2");
    const rolePhone2 = document.getElementsByClassName("rolePhone2")[0];
    rolePhone2.style.display = 'none'
    phone2.addEventListener("change", function() {
        if (this.value) {
            rolePhone2.style.display = 'none'
        } else {
            rolePhone2.style.display = 'block'
        }
    });
    //验证码校验
    // const code = document.getElementById("code");
    // const roleCode = document.getElementsByClassName("roleCode")[0];
    // roleCode.style.display = 'none'
    // code.addEventListener("change", function() {
    //   if (this.value) {
    //     roleCode.style.display = 'none'
    //   } else {
    //     roleCode.style.display = 'block'
    //   }
    // });

    var headers = document.getElementsByClassName('header-banner-button')[0];
    var closes = document.getElementsByClassName('close');
    var main = document.getElementsByClassName("main")[0];
    headers.onclick = function() {
        main.style.display = "block";
    }
    closes[0].onclick = function() {
        roleCpname.style.display = 'none'
        roleName.style.display = 'none'
        rolePhone.style.display = 'none'
        main.style.display = "none";
    }
    closes[1].onclick = function() {
        roleCpname.style.display = 'none'
        roleName.style.display = 'none'
        rolePhone.style.display = 'none'
        main.style.display = "none";
    }
    var headers2 = document.getElementsByClassName('header-banner-button2')[0];
    var closes2 = document.getElementsByClassName('close2');
    var main2 = document.getElementsByClassName("main2")[0];
    headers2.onclick = function() {
        main2.style.display = "block";
    }
    closes2[0].onclick = function() {
        main2.style.display = "none";
        roleCpname2.style.display = 'none'
        roleName2.style.display = 'none'
        rolePhone2.style.display = 'none'
    }
    closes2[1].onclick = function() {
        main2.style.display = "none";
        roleCpname2.style.display = 'none'
        roleName2.style.display = 'none'
        rolePhone2.style.display = 'none'
    }

    var tabs = document.getElementsByClassName('business-center-tab-center');
    var tabs2 = document.getElementsByClassName('color1');
    var tabs3 = document.getElementsByClassName('color2');
    for (let x = 0; x < tabs.length; x++) {
        tabs[x].onclick = function() {
            for (var i = 0; i < tabs.length; i++) {
                tabs[i].className = 'business-center-tab-center';
                this.className = 'business-center-tab-center business-center-tab-center-active'
                if (x == i) {
                    tabs2[i].className = 'color1 color3';
                    tabs3[i].className = 'color2 color3';
                    if (i == 0) {
                        $('.show-target1').hide()
                        $('.hide-target1').show()
                        $('.tab1').show()
                        $('.tab2').hide()
                        $('.tab3').hide()
                        $('.tab4').hide()
                        $('.show-target2').show()
                        $('.hide-target2').hide()
                        $('.show-target3').show()
                        $('.hide-target3').hide()
                        $('.show-target4').show()
                        $('.hide-target4').hide()
                        $('.triangle1').show()
                        $('.triangle2').hide()
                        $('.triangle3').hide()
                        $('.triangle4').hide()
                    } else if (i == 1) {
                        $('.tab2').show()
                        $('.tab1').hide()
                        $('.tab3').hide()
                        $('.tab4').hide()
                        $('.show-target2').hide()
                        $('.hide-target2').show()
                        $('.show-target1').show()
                        $('.hide-target1').hide()
                        $('.show-target3').show()
                        $('.hide-target3').hide()
                        $('.show-target4').show()
                        $('.hide-target4').hide()
                        $('.triangle2').show()
                        $('.triangle1').hide()
                        $('.triangle3').hide()
                        $('.triangle4').hide()
                    } else if (i == 2) {
                        $('.tab3').show()
                        $('.tab2').hide()
                        $('.tab1').hide()
                        $('.tab4').hide()
                        $('.show-target3').hide()
                        $('.hide-target3').show()
                        $('.show-target2').show()
                        $('.hide-target2').hide()
                        $('.show-target1').show()
                        $('.hide-target1').hide()
                        $('.show-target4').show()
                        $('.hide-target4').hide()
                        $('.triangle3').show()
                        $('.triangle1').hide()
                        $('.triangle2').hide()
                        $('.triangle4').hide()
                    } else if (i == 3) {
                        $('.tab4').show()
                        $('.tab2').hide()
                        $('.tab3').hide()
                        $('.tab1').hide()
                        $('.show-target4').hide()
                        $('.hide-target4').show()
                        $('.show-target2').show()
                        $('.hide-target2').hide()
                        $('.show-target3').show()
                        $('.hide-target3').hide()
                        $('.show-target1').show()
                        $('.hide-target1').hide()
                        $('.triangle4').show()
                        $('.triangle1').hide()
                        $('.triangle2').hide()
                        $('.triangle3').hide()
                    }
                } else {
                    tabs2[i].className = 'color1';
                    tabs3[i].className = 'color2';
                }
            }
        }
    }



    for (let x1 = 0; x1 < tabs.length; x1++) {
        tabsShow[x1].onclick = function() {
            for (var j = 0; j < tabs.length; j++) {
                tabsShow[j].className = 'business-center-tab-center2';
                this.className = 'business-center-tab-center2 business-center-tab-center-active2'

                if (x1 == j) {
                    tabs22[j].className = 'color12 color32';
                    tabs32[j].className = 'color22 color32';
                    tabs32[j].style.display = 'block';
                    if (j == 0) {
                        $('.show-target12').hide()
                        $('.hide-target12').show()
                        $('.tab12').show()
                        $('.tab22').hide()
                        $('.tab32').hide()
                        $('.tab42').hide()
                        $('.show-target22').show()
                        $('.hide-target22').hide()
                        $('.show-target32').show()
                        $('.hide-target32').hide()
                        $('.show-target42').show()
                        $('.hide-target42').hide()
                        $('.triangle12').show()
                        $('.triangle22').hide()
                        $('.triangle32').hide()
                        $('.triangle42').hide()
                    } else if (j == 1) {
                        $('.tab22').show()
                        $('.tab12').hide()
                        $('.tab32').hide()
                        $('.tab42').hide()
                        $('.show-target22').hide()
                        $('.hide-target22').show()
                        $('.show-target12').show()
                        $('.hide-target12').hide()
                        $('.show-target32').show()
                        $('.hide-target32').hide()
                        $('.show-target42').show()
                        $('.hide-target42').hide()
                        $('.triangle22').show()
                        $('.triangle12').hide()
                        $('.triangle32').hide()
                        $('.triangle42').hide()
                    } else if (j == 2) {
                        $('.tab32').show()
                        $('.tab22').hide()
                        $('.tab12').hide()
                        $('.tab42').hide()
                        $('.show-target32').hide()
                        $('.hide-target32').show()
                        $('.show-target22').show()
                        $('.hide-target22').hide()
                        $('.show-target12').show()
                        $('.hide-target12').hide()
                        $('.show-target42').show()
                        $('.hide-target42').hide()
                        $('.triangle32').show()
                        $('.triangle12').hide()
                        $('.triangle22').hide()
                        $('.triangle42').hide()
                    } else if (j == 3) {
                        $('.tab42').show()
                        $('.tab22').hide()
                        $('.tab32').hide()
                        $('.tab12').hide()
                        $('.show-target42').hide()
                        $('.hide-target42').show()
                        $('.show-target22').show()
                        $('.hide-target22').hide()
                        $('.show-target32').show()
                        $('.hide-target32').hide()
                        $('.show-target12').show()
                        $('.hide-target12').hide()
                        $('.triangle42').show()
                        $('.triangle12').hide()
                        $('.triangle22').hide()
                        $('.triangle32').hide()
                    }
                } else {
                    tabs22[j].className = 'color12';
                    tabs22[j].style.marginTop = '0';
                    tabs32[j].className = 'color22';
                    tabs32[j].style.display = 'none';
                }
            }
            swiper3.slideTo(x1 + 1)
        }
    }

    //pc端表单提交
    var submits = document.getElementsByClassName('submit')[0];
    submits.onclick = function() {
        var form = document.getElementById('myform');
        let cpname = form.elements['cpname'].value;
        let name = form.elements['name'].value;
        let phone = form.elements['phone'].value;
        if (cpname) {
            roleCpname.style.display = 'none'
        } else {
            roleCpname.style.display = 'block'
        }
        if (name) {
            roleName.style.display = 'none'
        } else {
            roleName.style.display = 'block'
        }
        if (phone) {
            rolePhone.style.display = 'none'
        } else {
            rolePhone.style.display = 'block'
        }
        if (cpname && name && phone) {
            $.ajax({
                url: "/wp-admin/admin-ajax.php",
                type: 'POST',
                crossDomain: true,
                data: {
                    "cpname": cpname,
                    "name": name,
                    "phone": phone,
                    "action": "hcyl_constact_ajax_action"
                },
                dataType: "json",
                success: function(res) {
                    console.log("res", res)
                    if (res.code == 200) {
                        alert("操作成功")
                        main.style.display = "none";
                    } else {
                        alert("操作成功,请联系企微客服")
                    }
                },
                error: function(err) {
                    console.log("err", err)
                }

            })
        }
    }
    //移动端表单提交
    var submits2 = document.getElementsByClassName('submit2')[0];
    submits2.onclick = function() {
        console.log(666)
        var form2 = document.getElementById('myform1');
        let cpname = form2.elements['cpname2'].value;
        let name = form2.elements['name2'].value;
        let phone = form2.elements['phone2'].value;
        if (cpname) {
            roleCpname2.style.display = 'none'
        } else {
            roleCpname2.style.display = 'block'
        }
        if (name) {
            roleName2.style.display = 'none'
        } else {
            roleName2.style.display = 'block'
        }
        if (phone) {
            rolePhone2.style.display = 'none'
        } else {
            rolePhone2.style.display = 'block'
        }

        if (cpname && name && phone) {
            $.ajax({
                url: "/wp-admin/admin-ajax.php",
                type: 'POST',
                crossDomain: true,
                data: {
                    "cpname": cpname,
                    "name": name,
                    "phone": phone,
                    "action": "hcyl_constact_ajax_action"
                },
                dataType: "json",
                success: function(res) {
                    console.log("res", res)
                    if (res.code == 200) {
                        alert("操作成功")
                        main.style.display = "none";
                    } else {
                        alert("操作成功,请联系企微客服")
                    }
                },
                error: function(err) {
                    console.log("err", err)
                }

            })
        }
    }
</script>

</body>

</html>