<?php

/**
 * hcylsoftcn Theme Customizer
 *
 * @package hcylsoftcn
 */

/**
 * Add postMessage support for site title and description for the Theme Customizer.
 *
 * @param WP_Customize_Manager $wp_customize Theme Customizer object.
 */
function hcylsoftcn_customize_register($wp_customize)
{

	$wp_customize->get_setting('blogname')->transport         = 'postMessage';
	$wp_customize->get_setting('blogdescription')->transport  = 'postMessage';
	$wp_customize->get_setting('header_textcolor')->transport = 'postMessage';


	if (isset($wp_customize->selective_refresh)) {
		$wp_customize->selective_refresh->add_partial(
			'blogname',
			array(
				'selector'        => '.site-title a',
				'render_callback' => 'hcylsoftcn_customize_partial_blogname',
			)
		);
		$wp_customize->selective_refresh->add_partial(
			'blogdescription',
			array(
				'selector'        => '.site-description',
				'render_callback' => 'hcylsoftcn_customize_partial_blogdescription',
			)
		);
	}
	$sections =  $wp_customize->sections();
	foreach ($sections as $section) {
		//移除chuang zhi
		$wp_customize->remove_section('colors');
		$wp_customize->remove_section('header_image');
		$wp_customize->remove_section('static_front_page');
	}



	//首页轮播
	$wp_customize->add_panel('hcylsoft', array(
		'title' => '网站设置'
	));

	// 添加自定义Banner面板
	$wp_customize->add_panel('hcylsoft_banners', array(
		'title' => '自定义Banner',
		'description' => '配置各个页面的Banner图片',
		'priority' => 15,
	));

	setting_banners($wp_customize);

	companyProfile($wp_customize);

	aboutUsCompanyProfile($wp_customize);

	//设置video
	setting_bipm_video($wp_customize);

	//企微生态解决方案页面banner
	setting_wecom_solution_banner($wp_customize);

	//金蝶云容器页面banner
	setting_kingdee_cloud_banner($wp_customize);

	//企业网盘页面banner
	setting_cloud_disk_banner($wp_customize);

	//资质证照自助打印页面banner
	setting_seal_print_banner($wp_customize);
}
add_action('customize_register', 'hcylsoftcn_customize_register');

// 首页banner
function setting_banners($wp_customize)
{


	$wp_customize->add_section('hcylsoft_banner', array(
		'title' => '首页轮播',
		'panel' => 'hcylsoft'
	));



	foreach (banners() as $banner) {

		$wp_customize->add_setting($banner['id'], array(
			'type' => 'theme_mod', // or 'option'
			'capability' => 'edit_theme_options',
			'theme_supports' => '', // Rarely needed.
			'default' => '',
			'transport' => 'refresh', // or postMessage
			'sanitize_callback' => '',
			'sanitize_js_callback' => '', // Basically to_json.
		));

		$wp_customize->add_control(new WP_Customize_Image_Control($wp_customize, $banner['id'], array(
			'label' => $banner['title'],
			'section' => 'hcylsoft_banner',
			'mime_type' => 'image',
		)));
	}
}

//首页公司简介
function companyProfile($wp_customize)
{

	$wp_customize->add_section('hcylsoft_company_profile', array(
		'title' => '首页公司简介',
		'panel' => 'hcylsoft'
	));

	$wp_customize->add_setting('companyprofile', array(
		'type' => 'theme_mod', // or 'option'
		'capability' => 'edit_theme_options',
		'theme_supports' => '', // Rarely needed.
		'default' => '',
		'transport' => 'postMessage', // or postMessage
		'sanitize_callback' => '',
		'sanitize_js_callback' => '', // Basically to_json.
	));

	$wp_customize->add_control('companyprofile', array(
		'type' => 'textarea',
		'label' => '公司简介',
		'input_attrs' => array(
			'placeholder' => '请输入公司简介'
		),
		'section' => 'hcylsoft_company_profile',
	));

	$wp_customize->add_setting('companyprofile_url', array(
		'type' => 'theme_mod', // or 'option'
		'capability' => 'edit_theme_options',
		'theme_supports' => '', // Rarely needed.
		'default' => '',
		'transport' => 'postMessage', // or postMessage
		'sanitize_callback' => '',
		'sanitize_js_callback' => '', // Basically to_json.
	));

	//	  $wp_customize->add_control('companyprofile_url',array(
	//		'type' => 'input',
	//		'label' => '公司简介链接',
	//		'input_attrs' => array(
	//			'placeholder' => '请输入公司链接',
	//			'style' => 'width:100%'
	//		),
	//		'section' => 'hcylsoft_company_profile',
	//	  ));

	$wp_customize->add_setting('companyprofile_img', array(
		'type' => 'theme_mod', // or 'option'
		'capability' => 'edit_theme_options',
		'theme_supports' => '', // Rarely needed.
		'default' => '',
		'transport' => 'refresh', // or postMessage
		'sanitize_callback' => '',
		'sanitize_js_callback' => '', // Basically to_json.
	));

	$wp_customize->add_control(new WP_Customize_Image_Control($wp_customize, 'companyprofile_img', array(
		'label' => '公司简介图片',
		'section' => 'hcylsoft_company_profile',
		'mime_type' => 'image',
	)));

	$wp_customize->selective_refresh->add_partial(
		'companyprofile',
		array(
			'selector'        => '.company-profile-content',
			'render_callback' => function () {
				return get_theme_mod('companyprofile');
			},
		)
	);

	//	$wp_customize->selective_refresh->add_partial(
	//		'companyprofile_url',
	//		array(
	//			'selector'        => '.company-profile-l a',
	//			'render_callback' => function() {
	//				return get_theme_mod('companyprofile_url');
	//			},
	//		)
	//	);

	$wp_customize->selective_refresh->add_partial(
		'companyprofile_img',
		array(
			'selector'        => '.syjj',
			'render_callback' => function () {
				return get_theme_mod('companyprofile_img');
			},
		)
	);
}

//关于我们页面公司简介
function aboutUsCompanyProfile($wp_customize)
{

	$wp_customize->add_section('hcylsoft_aboutus_company_intro', array(
		'title' => '关于我们-公司简介',
		'panel' => 'hcylsoft'
	));

	$wp_customize->add_setting('aboutus_company_intro', array(
		'type' => 'theme_mod',
		'capability' => 'edit_theme_options',
		'theme_supports' => '',
		'default' => '',
		'transport' => 'postMessage',
		'sanitize_callback' => '',
		'sanitize_js_callback' => '',
	));

	$wp_customize->add_control('aboutus_company_intro', array(
		'type' => 'textarea',
		'label' => '关于我们页面公司简介',
		'input_attrs' => array(
			'placeholder' => '请输入关于我们页面的公司简介内容'
		),
		'section' => 'hcylsoft_aboutus_company_intro',
	));

	$wp_customize->selective_refresh->add_partial(
		'aboutus_company_intro',
		array(
			'selector'        => '.company-intro-content',
			'render_callback' => function () {
				return get_theme_mod('aboutus_company_intro');
			},
		)
	);
}

//设置视频
function  setting_bipm_video($wp_customize)
{



	$wp_customize->add_section('hcylsoft_bipm_setting', array(
		'title' => '建智管配置',
		'panel' => 'hcylsoft'
	));
	$wp_customize->add_setting('bipmvideo', array(
		'type' => 'theme_mod', // or 'option'
		'capability' => 'edit_theme_options',
		'theme_supports' => '', // Rarely needed.
		'default' => '',
		'transport' => 'refresh', // or postMessage
		'sanitize_callback' => '',
		'sanitize_js_callback' => '', // Basically to_json.
	));
	$wp_customize->add_control(new WP_Customize_Media_Control($wp_customize, 'bipmvideo', array(
		'label' => '宣传视频',
		'section' => 'hcylsoft_bipm_setting',
		'mime_type' => 'video',
	)));

	$wp_customize->selective_refresh->add_partial(
		'bipmvideo',
		array(
			'selector'        => '.title-center-swiper-right',
		)
	);
}

//企微生态解决方案页面banner配置
function setting_wecom_solution_banner($wp_customize)
{
	$wp_customize->add_section('hcylsoft_wecom_solution_banner', array(
		'title' => '企微生态解决方案',
		'panel' => 'hcylsoft_banners',
		'description' => '配置企微生态解决方案页面的Banner图片'
	));

	// Banner图片设置
	$wp_customize->add_setting('wecom_solution_banner', array(
		'type' => 'theme_mod',
		'capability' => 'edit_theme_options',
		'default' => '',
		'transport' => 'postMessage',
		'sanitize_callback' => 'esc_url_raw',
	));

	$wp_customize->add_control(new WP_Customize_Image_Control($wp_customize, 'wecom_solution_banner', array(
		'label' => '企微生态解决方案Banner图片',
		'description' => '上传企微生态解决方案页面的Banner图片，建议尺寸：1920x400像素',
		'section' => 'hcylsoft_wecom_solution_banner',
		'mime_type' => 'image',
	)));

	// 添加 Selective Refresh 支持
	if (isset($wp_customize->selective_refresh)) {
		$wp_customize->selective_refresh->add_partial('wecom_solution_banner', array(
			'selector' => '.wecom-solution .banner-item img',
			'render_callback' => 'wecom_solution_banner_partial_render',
		));
	}
}

//金蝶云容器页面banner配置
function setting_kingdee_cloud_banner($wp_customize)
{
	$wp_customize->add_section('hcylsoft_kingdee_cloud_banner', array(
		'title' => '金蝶云容器',
		'panel' => 'hcylsoft_banners',
		'description' => '配置金蝶云容器页面的Banner图片'
	));

	// Banner图片设置
	$wp_customize->add_setting('kingdee_cloud_banner', array(
		'type' => 'theme_mod',
		'capability' => 'edit_theme_options',
		'default' => '',
		'transport' => 'postMessage',
		'sanitize_callback' => 'esc_url_raw',
	));

	$wp_customize->add_control(new WP_Customize_Image_Control($wp_customize, 'kingdee_cloud_banner', array(
		'label' => '金蝶云容器Banner图片',
		'description' => '上传金蝶云容器页面的Banner图片，建议尺寸：1920x650像素',
		'section' => 'hcylsoft_kingdee_cloud_banner',
		'mime_type' => 'image',
	)));

	// 添加 Selective Refresh 支持
	if (isset($wp_customize->selective_refresh)) {
		$wp_customize->selective_refresh->add_partial('kingdee_cloud_banner', array(
			'selector' => '.kingdee-cloud .banner-item img',
			'render_callback' => 'kingdee_cloud_banner_partial_render',
		));
	}
}

//企业网盘页面banner配置
function setting_cloud_disk_banner($wp_customize)
{
	$wp_customize->add_section('hcylsoft_cloud_disk_banner', array(
		'title' => '企业网盘',
		'panel' => 'hcylsoft_banners',
		'description' => '配置企业网盘页面的Banner图片'
	));

	// Banner图片设置
	$wp_customize->add_setting('cloud_disk_banner', array(
		'type' => 'theme_mod',
		'capability' => 'edit_theme_options',
		'default' => '',
		'transport' => 'postMessage',
		'sanitize_callback' => 'esc_url_raw',
	));

	$wp_customize->add_control(new WP_Customize_Image_Control($wp_customize, 'cloud_disk_banner', array(
		'label' => '企业网盘Banner图片',
		'description' => '上传企业网盘页面的Banner图片，建议尺寸：1920x400像素',
		'section' => 'hcylsoft_cloud_disk_banner',
		'mime_type' => 'image',
	)));

	// 添加 Selective Refresh 支持
	if (isset($wp_customize->selective_refresh)) {
		$wp_customize->selective_refresh->add_partial('cloud_disk_banner', array(
			'selector' => '.cloud-disk .banner-container img',
			'render_callback' => 'cloud_disk_banner_partial_render',
		));
	}
}

//资质证照自助打印页面banner配置
function setting_seal_print_banner($wp_customize)
{
	$wp_customize->add_section('hcylsoft_seal_print_banner', array(
		'title' => '资质证照自助打印',
		'panel' => 'hcylsoft_banners',
		'description' => '配置资质证照自助打印页面的Banner图片'
	));

	// Banner图片设置
	$wp_customize->add_setting('seal_print_banner', array(
		'type' => 'theme_mod',
		'capability' => 'edit_theme_options',
		'default' => '',
		'transport' => 'postMessage',
		'sanitize_callback' => 'esc_url_raw',
	));

	$wp_customize->add_control(new WP_Customize_Image_Control($wp_customize, 'seal_print_banner', array(
		'label' => '资质证照自助打印Banner图片',
		'description' => '上传资质证照自助打印页面的Banner图片，建议尺寸：1920x400像素',
		'section' => 'hcylsoft_seal_print_banner',
		'mime_type' => 'image',
	)));

	// 添加 Selective Refresh 支持
	if (isset($wp_customize->selective_refresh)) {
		$wp_customize->selective_refresh->add_partial('seal_print_banner', array(
			'selector' => '.seal-print .banner-container img',
			'render_callback' => 'seal_print_banner_partial_render',
		));
	}
}

/**
 * 企微生态解决方案Banner的Selective Refresh渲染回调
 */
function wecom_solution_banner_partial_render()
{
	return '<img src="' . esc_url(get_wecom_solution_banner()) . '" alt="企业微信生态解决方案banner">';
}

/**
 * 金蝶云容器Banner的Selective Refresh渲染回调
 */
function kingdee_cloud_banner_partial_render()
{
	return '<img src="' . esc_url(get_kingdee_cloud_banner()) . '" alt="金蝶云容器解决方案banner">';
}

/**
 * 企业网盘Banner的Selective Refresh渲染回调
 */
function cloud_disk_banner_partial_render()
{
	return '<img src="' . esc_url(get_cloud_disk_banner()) . '" alt="企业网盘">';
}

/**
 * 资质证照自助打印Banner的Selective Refresh渲染回调
 */
function seal_print_banner_partial_render()
{
	return '<img src="' . esc_url(get_seal_print_banner()) . '" alt="证照打印一体机">';
}

/**
 * Render the site title for the selective refresh partial.
 *
 * @return void
 */
function hcylsoftcn_customize_partial_blogname()
{
	bloginfo('name');
}

/**
 * Render the site tagline for the selective refresh partial.
 *
 * @return void
 */
function hcylsoftcn_customize_partial_blogdescription()
{
	bloginfo('description');
}

/**
 * Binds JS handlers to make Theme Customizer preview reload changes asynchronously.
 */
function hcylsoftcn_customize_preview_js()
{
	wp_enqueue_script('hcylsoftcn-customizer', get_template_directory_uri() . '/js/customizer.js', array('customize-preview'), _S_VERSION, true);
}
add_action('customize_preview_init', 'hcylsoftcn_customize_preview_js');
